import type { Metada<PERSON> } from "next";
import { fetchRegions } from "@/lib/api";
import { RegionCarousel } from "./RegionCarousel";
import type { RegionResponse } from "../types/region";
import StylizedGlobe from "@/components/StylizedGlobe"; // default import

export const revalidate = process.env.NODE_ENV === "production" ? 3600 : 0;
export const metadata: Metadata = {
  title: "All Pokémon Regions",
  description: "Browse all regions in the Pokémon world",
};

export default async function RegionIndex() {
  const data: RegionResponse = await fetchRegions();

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Pokémon Regions</h1>
      <div className="grid gap-4">
        <StylizedGlobe /> {/* client-only hook component */}
        <RegionCarousel results={data.results} />
      </div>
    </div>
  );
}
