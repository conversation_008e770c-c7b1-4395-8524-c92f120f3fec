// src/app/region/page.tsx

import type { Metadata } from "next";

export const revalidate = process.env.NODE_ENV === "production" ? 60 : 0;

export const metadata: Metadata = {
  title: "All Pokémon Regions",
  description: "Browse all regions in the Pokémon world",
};

const RegionIndex = async () => {
  const res = await fetch("https://pokeapi.co/api/v2/region");

  const data = await res.json();

  console.log(data);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">{data}</h1>
    </div>
  );
};

export default RegionIndex;
