// src/app/region/page.tsx

import type { Metadata } from "next";
import { fetchRegions } from "@/lib/api";

// Type definitions for PokeAPI
interface Region {
  name: string;
  url: string;
}

interface RegionResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Region[];
}

export const revalidate = process.env.NODE_ENV === "production" ? 60 : 0;

export const metadata: Metadata = {
  title: "All Pokémon Regions",
  description: "Browse all regions in the Pokémon world",
};

const RegionIndex = async () => {
  try {
    // Use the utility function with retry logic and better error handling
    const data: RegionResponse = await fetchRegions();

    console.log(data);

    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Pokémon Regions</h1>
        <div className="grid gap-4">
          {data.results?.map((region: Region) => (
            <div key={region.name} className="p-4 border rounded-lg">
              <h2 className="text-lg font-semibold capitalize">
                {region.name}
              </h2>
              <a
                href={`/region/${region.name}`}
                className="text-blue-500 hover:underline"
              >
                View Details
              </a>
            </div>
          ))}
        </div>
      </div>
    );
  } catch (error) {
    console.error("Failed to fetch regions:", error);

    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Pokémon Regions</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p className="font-bold">Error loading regions</p>
          <p>
            Unable to connect to PokeAPI. Please check your internet connection
            and try again.
          </p>
          <details className="mt-2">
            <summary className="cursor-pointer">Technical details</summary>
            <pre className="mt-2 text-sm">
              {error instanceof Error ? error.message : "Unknown error"}
            </pre>
          </details>
        </div>
      </div>
    );
  }
};

export default RegionIndex;
