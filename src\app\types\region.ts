export interface RegionListResponse {
  results: { name: string }[];
}

export interface RegionInfo {
  name: string;
  description: string;
}

export interface Region {
  name: string;
  url: string;
}

export interface RegionResponse {
  results: Array<{
    name: string;
    url: string;
  }>;
  count: number;
  next: string | null;
  previous: string | null;
}

// (Optional) The exact shape PokeAPI returns for `/region/{name}`.
// Handy if you want full type-safety when accessing nested properties.
export interface PokeApiRegionResponse {
  name: string;
  names: Array<{
    name: string;
    language: { name: string };
  }>;
  pokedexes: Array<{ url: string }>;
  // …you can add other fields here as you need them
}
