// src/lib/api.ts
// Utility functions for API calls with better error handling

export async function fetchWithRetry(
  url: string,
  options: RequestInit = {},
  retries = 3
): Promise<Response> {
  const defaultOptions: RequestInit = {
    headers: {
      "User-Agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      Accept: "application/json",
      ...options.headers,
    },
    signal: AbortSignal.timeout(15000), // 15 second timeout
    ...options,
  };

  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url, defaultOptions);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response;
    } catch (error) {
      console.warn(`Fetch attempt ${i + 1} failed:`, error);

      if (i === retries - 1) {
        throw error;
      }

      // Wait before retrying (exponential backoff)
      await new Promise((resolve) =>
        setTimeout(resolve, Math.pow(2, i) * 1000)
      );
    }
  }

  throw new Error("All retry attempts failed");
}

// PokeAPI specific functions with HTTPS/HTTP fallback
export async function fetchRegions() {
  // Try HTTPS first, fallback to HTTP if SSL issues occur
  try {
    const response = await fetchWithRetry("https://pokeapi.co/api/v2/region");
    return response.json();
  } catch (error) {
    console.warn("HTTPS failed, trying HTTP:", error);
    const response = await fetchWithRetry("http://pokeapi.co/api/v2/region");
    return response.json();
  }
}

export async function fetchRegion(name: string) {
  // Try HTTPS first, fallback to HTTP if SSL issues occur
  try {
    const response = await fetchWithRetry(
      `https://pokeapi.co/api/v2/region/${name}`
    );
    return response.json();
  } catch (error) {
    console.warn("HTTPS failed, trying HTTP:", error);
    const response = await fetchWithRetry(
      `http://pokeapi.co/api/v2/region/${name}`
    );
    return response.json();
  }
}
