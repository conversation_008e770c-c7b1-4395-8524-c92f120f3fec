// src/lib/api.ts
// Utility functions for API calls with better error handling

export async function fetchWithRetry(
  url: string,
  options: RequestInit = {},
  retries = 3
): Promise<Response> {
  const defaultOptions: RequestInit = {
    headers: {
      "User-Agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      Accept: "application/json, text/plain, */*",
      "Accept-Language": "en-US,en;q=0.9",
      "Cache-Control": "no-cache",
      Pragma: "no-cache",
      ...options.headers,
    },
    signal: AbortSignal.timeout(20000), // 20 second timeout
    ...options,
  };

  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url, defaultOptions);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response;
    } catch (error) {
      console.warn(`Fetch attempt ${i + 1} failed:`, error);

      if (i === retries - 1) {
        throw error;
      }

      // Wait before retrying (exponential backoff)
      await new Promise((resolve) =>
        setTimeout(resolve, Math.pow(2, i) * 1000)
      );
    }
  }

  throw new Error("All retry attempts failed");
}

// PokeAPI specific functions - using alternative API or fallback data
export async function fetchRegions() {
  // Since your network is blocking PokeAPI, let's use fallback data
  // or try alternative approaches

  try {
    // Use local API route to avoid SSL issues
    const baseUrl =
      process.env.NODE_ENV === "production"
        ? "https://your-domain.com"
        : "http://localhost:3000";
    const response = await fetch(`https://pokeapi.co/api/v2/region/`, {
      headers: {
        Accept: "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.warn("Local API failed, using fallback data:", error);

    // Return fallback data when API is blocked
    return {
      count: 9,
      next: null,
      previous: null,
      results: [
        { name: "kanto", url: "https://pokeapi.co/api/v2/region/1/" },
        { name: "johto", url: "https://pokeapi.co/api/v2/region/2/" },
        { name: "hoenn", url: "https://pokeapi.co/api/v2/region/3/" },
        { name: "sinnoh", url: "https://pokeapi.co/api/v2/region/4/" },
        { name: "unova", url: "https://pokeapi.co/api/v2/region/5/" },
        { name: "kalos", url: "https://pokeapi.co/api/v2/region/6/" },
        { name: "alola", url: "https://pokeapi.co/api/v2/region/7/" },
        { name: "galar", url: "https://pokeapi.co/api/v2/region/8/" },
        { name: "paldea", url: "https://pokeapi.co/api/v2/region/9/" },
      ],
    };
  }
}

export async function fetchRegion(name: string) {
  // Use local API route to avoid SSL issues
  try {
    const baseUrl =
      process.env.NODE_ENV === "production"
        ? "https://your-domain.com"
        : "http://localhost:3000";
    const response = await fetch(`${baseUrl}/api/pokemon/region/${name}`, {
      headers: {
        Accept: "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log("data returned from fetchRegion", data);
    return data;
  } catch (error) {
    console.warn(
      `Local API failed for region ${name}, using fallback data:`,
      error
    );

    // Return fallback data for individual regions
    const fallbackRegions: Record<string, any> = {
      kanto: {
        id: 1,
        name: "kanto",
        names: [{ name: "Kanto", language: { name: "en" } }],
        pokedexes: [],
        locations: [],
      },
      johto: {
        id: 2,
        name: "johto",
        names: [{ name: "Johto", language: { name: "en" } }],
        pokedexes: [],
        locations: [],
      },
      hoenn: {
        id: 3,
        name: "hoenn",
        names: [{ name: "Hoenn", language: { name: "en" } }],
        pokedexes: [],
        locations: [],
      },
      sinnoh: {
        id: 4,
        name: "sinnoh",
        names: [{ name: "Sinnoh", language: { name: "en" } }],
        pokedexes: [],
        locations: [],
      },
      unova: {
        id: 5,
        name: "unova",
        names: [{ name: "Unova", language: { name: "en" } }],
        pokedexes: [],
        locations: [],
      },
      kalos: {
        id: 6,
        name: "kalos",
        names: [{ name: "Kalos", language: { name: "en" } }],
        pokedexes: [],
        locations: [],
      },
      alola: {
        id: 7,
        name: "alola",
        names: [{ name: "Alola", language: { name: "en" } }],
        pokedexes: [],
        locations: [],
      },
      galar: {
        id: 8,
        name: "galar",
        names: [{ name: "Galar", language: { name: "en" } }],
        pokedexes: [],
        locations: [],
      },
      paldea: {
        id: 9,
        name: "paldea",
        names: [{ name: "Paldea", language: { name: "en" } }],
        pokedexes: [],
        locations: [],
      },
    };

    const fallbackData = fallbackRegions[name.toLowerCase()];
    if (fallbackData) {
      return fallbackData;
    }

    // If region not found in fallback, throw error
    throw new Error(`Region ${name} not found`);
  }
}
