/** @type {import('next').NextConfig} */
const nextConfig = {
  // Disable SSL verification for development
  experimental: {
    serverComponentsExternalPackages: [],
  },
  // Configure for better API handling
  async rewrites() {
    return [
      {
        source: '/api/pokemon/:path*',
        destination: 'https://pokeapi.co/api/v2/:path*',
      },
    ];
  },
  // Headers for better CORS handling
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,OPTIONS,PATCH,DELETE,POST,PUT' },
          { key: 'Access-Control-Allow-Headers', value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version' },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
