import * as React from "react";

interface RegionCarouselProps {
  results: [{ name: string; url: string }];
}

import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

export function RegionCarousel({ results }: RegionCarouselProps) {
  return (
    <Carousel className="w-full max-w-xs">
      <CarouselContent>
        {results.map((region, index) => (
          <CarouselItem key={index}>
            <div className="p-1">
              <Card>
                {region.name.toLocaleUpperCase()}
                <CardContent className="flex aspect-square items-center justify-center p-6">
                  <span className="text-4xl font-semibold">{region.name}</span>
                </CardContent>
              </Card>
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious />
      <CarouselNext />
    </Carousel>
  );
}
