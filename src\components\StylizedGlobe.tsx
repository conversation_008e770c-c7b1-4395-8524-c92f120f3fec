// components/StylizedGlobe.tsx
"use client";

import { useEffect, useRef } from "react";
import Globe from "globe.gl";

export default function StylizedGlobe() {
  const globeRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!globeRef.current) return;

    const globeInstance = new Globe(globeRef.current);
    globeInstance
      .globeImageUrl("/A_2D_digital_illustration_displays_a_world_map_in_.png")
      .backgroundColor("#000")
      .showAtmosphere(false)
      .enablePointerInteraction(false)
      .pointOfView({ altitude: 2.5 });

    return () => {
      globeRef.current?.replaceChildren();
    };
  }, []);

  return <div ref={globeRef} style={{ width: "100%", height: "100vh" }} />;
}
