// src/app/region/[regionName]/page.tsx
import { notFound } from "next/navigation";
import type { RegionInfo, PokeApiRegionResponse } from "@/app/types/region";
import { fetchRegion } from "@/lib/api";

export const dynamicParams = true;
export async function generateStaticParams() {
  try {
    const res = await fetch("https://pokeapi.co/api/v2/region", {
      cache:
        process.env.NODE_ENV === "development" ? "no-store" : "force-cache",
    });
    if (!res.ok) throw new Error();
    const data: { results: { name: string }[] } = await res.json();
    return data.results.map((r) => ({ regionName: r.name }));
  } catch {
    // fallback to the main eight if the API is down
    return [
      "kanto",
      "johto",
      "hoenn",
      "sinnoh",
      "unova",
      "kalos",
      "alola",
      "galar",
    ].map((regionName) => ({ regionName }));
  }
}

export default async function RegionPage({
  params,
}: {
  params: Promise<{ regionName: string }>;
}) {
  const { regionName } = await params;

  let regionData: PokeApiRegionResponse;
  try {
    regionData = await fetchRegion(regionName);
  } catch {
    return (
      <div className="p-6">
        <h1 className="text-3xl font-bold mb-2">Error Loading Region</h1>
        <p className="text-lg text-red-600">
          Could not fetch data for “{regionName}.”
          <br />
          Please check your network or try again later.
        </p>
      </div>
    );
  }

  // build your RegionInfo
  const name =
    regionData.names.find((n) => n.language.name === "en")?.name ??
    regionData.name;

  // fetch an English description from the first pokedex, if possible
  let description = "No description available.";
  const pokedexUrl = regionData.pokedexes[0]?.url;
  if (pokedexUrl) {
    try {
      const dexRes = await fetch(pokedexUrl);
      if (dexRes.ok) {
        const dexJson: {
          descriptions: Array<{
            description: string;
            language: { name: string };
          }>;
        } = await dexRes.json();
        description =
          dexJson.descriptions.find((d) => d.language.name === "en")
            ?.description ?? description;
      }
    } catch {
      /* ignore */
    }
  }

  const region: RegionInfo = { name, description };

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-2 capitalize">{region.name}</h1>
      <p className="text-lg text-gray-700">{region.description}</p>
    </div>
  );
}
