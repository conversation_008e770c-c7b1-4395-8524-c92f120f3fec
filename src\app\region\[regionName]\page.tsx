// src/app/region/[regionName]/page.tsx
import { notFound } from "next/navigation";
import type { RegionInfo, PokeApiRegionResponse } from "@/app/types/region";
import { fetchRegion } from "@/lib/api";

export const dynamicParams = true;
export async function generateStaticParams() {
  // Use fallback data to avoid SSL issues during build
  return [
    "kanto",
    "johto",
    "hoenn",
    "sinnoh",
    "unova",
    "kalos",
    "alola",
    "galar",
    "paldea",
  ].map((regionName) => ({ regionName }));
}

export default async function RegionPage({
  params,
}: {
  params: Promise<{ regionName: string }>;
}) {
  const { regionName } = await params;

  let regionData: PokeApiRegionResponse;
  try {
    regionData = await fetchRegion(regionName);

    console.log(regionData);
  } catch {
    return (
      <div className="p-6">
        <h1 className="text-3xl font-bold mb-2">Error Loading Region</h1>
        <p className="text-lg text-red-600">
          Could not fetch data for “{regionName}.”
          <br />
          Please check your network or try again later.
        </p>
      </div>
    );
  }

  // build your RegionInfo
  const name =
    regionData.names.find((n) => n.language.name === "en")?.name ??
    regionData.name;

  // Use a simple description since external API calls have SSL issues
  let description = `The ${name} region is one of the main regions in the Pokémon world.`;

  // Add specific descriptions for known regions
  const regionDescriptions: Record<string, string> = {
    kanto:
      "The Kanto region is the setting of the original Pokémon games and is known for its diverse landscapes and the Pokémon League.",
    johto:
      "The Johto region is known for its traditional culture and is home to many legendary Pokémon.",
    hoenn:
      "The Hoenn region features a tropical climate with many islands and is known for its contests and battles.",
    sinnoh:
      "The Sinnoh region is a large landmass with diverse terrain, from snowy mountains to lush forests.",
    unova:
      "The Unova region is known for its urban development and seasonal changes throughout the year.",
    kalos:
      "The Kalos region is inspired by France and is known for its beauty, fashion, and the discovery of Mega Evolution.",
    alola:
      "The Alola region consists of tropical islands and is known for its unique Pokémon variants and Z-Moves.",
    galar:
      "The Galar region is inspired by Great Britain and is known for its industrial cities and Dynamax phenomenon.",
    paldea:
      "The Paldea region is known for its open-world exploration and diverse ecosystems.",
  };

  description = regionDescriptions[regionName.toLowerCase()] || description;

  const region: RegionInfo = { name, description };

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-2 capitalize">{region.name}</h1>
      <p className="text-lg text-gray-700">{region.description}</p>
    </div>
  );
}
