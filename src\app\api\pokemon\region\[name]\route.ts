// src/app/api/pokemon/region/[name]/route.ts
import { NextResponse } from 'next/server';

// Fallback data for individual regions
const FALLBACK_REGION_DATA: Record<string, any> = {
  kanto: {
    id: 1,
    name: 'kanto',
    names: [{ name: 'Kanto', language: { name: 'en' } }],
    pokedexes: [],
    locations: [],
  },
  johto: {
    id: 2,
    name: 'johto',
    names: [{ name: '<PERSON><PERSON><PERSON>', language: { name: 'en' } }],
    pokedexes: [],
    locations: [],
  },
  hoenn: {
    id: 3,
    name: 'hoenn',
    names: [{ name: 'Hoenn', language: { name: 'en' } }],
    pokedexes: [],
    locations: [],
  },
  sinnoh: {
    id: 4,
    name: 'sinnoh',
    names: [{ name: 'Sinnoh', language: { name: 'en' } }],
    pokedexes: [],
    locations: [],
  },
  unova: {
    id: 5,
    name: 'unova',
    names: [{ name: 'Unova', language: { name: 'en' } }],
    pokedexes: [],
    locations: [],
  },
  kalos: {
    id: 6,
    name: 'kalos',
    names: [{ name: 'Ka<PERSON>', language: { name: 'en' } }],
    pokedexes: [],
    locations: [],
  },
  alola: {
    id: 7,
    name: 'alola',
    names: [{ name: 'Alola', language: { name: 'en' } }],
    pokedexes: [],
    locations: [],
  },
  galar: {
    id: 8,
    name: 'galar',
    names: [{ name: 'Galar', language: { name: 'en' } }],
    pokedexes: [],
    locations: [],
  },
  paldea: {
    id: 9,
    name: 'paldea',
    names: [{ name: 'Paldea', language: { name: 'en' } }],
    pokedexes: [],
    locations: [],
  },
};

export async function GET(
  request: Request,
  { params }: { params: Promise<{ name: string }> }
) {
  const { name } = await params;
  const regionName = name.toLowerCase();

  try {
    // Try to fetch from PokeAPI with aggressive timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const response = await fetch(`https://pokeapi.co/api/v2/region/${regionName}`, {
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
      },
    });
  } catch (error) {
    console.warn(`PokeAPI failed for region ${regionName}, using fallback data:`, error);
    
    // Return fallback data if available
    const fallbackData = FALLBACK_REGION_DATA[regionName];
    if (fallbackData) {
      return NextResponse.json(fallbackData, {
        headers: {
          'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
          'X-Fallback': 'true',
        },
      });
    }
    
    // Return 404 if region not found
    return NextResponse.json(
      { error: `Region ${regionName} not found` },
      { status: 404 }
    );
  }
}
