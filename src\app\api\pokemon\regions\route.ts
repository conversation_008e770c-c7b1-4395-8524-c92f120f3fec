// src/app/api/pokemon/regions/route.ts
import { NextResponse } from 'next/server';

// Fallback data for when external API fails
const FALLBACK_REGIONS = {
  count: 9,
  next: null,
  previous: null,
  results: [
    { name: 'kanto', url: 'https://pokeapi.co/api/v2/region/1/' },
    { name: 'johto', url: 'https://pokeapi.co/api/v2/region/2/' },
    { name: 'hoenn', url: 'https://pokeapi.co/api/v2/region/3/' },
    { name: 'sinnoh', url: 'https://pokeapi.co/api/v2/region/4/' },
    { name: 'unova', url: 'https://pokeapi.co/api/v2/region/5/' },
    { name: 'kalos', url: 'https://pokeapi.co/api/v2/region/6/' },
    { name: 'alola', url: 'https://pokeapi.co/api/v2/region/7/' },
    { name: 'galar', url: 'https://pokeapi.co/api/v2/region/8/' },
    { name: 'paldea', url: 'https://pokeapi.co/api/v2/region/9/' },
  ],
};

export async function GET() {
  try {
    // Try to fetch from PokeAPI with aggressive timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const response = await fetch('https://pokeapi.co/api/v2/region', {
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
        'Cache-Control': 'no-cache',
      },
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
      },
    });
  } catch (error) {
    console.warn('PokeAPI failed, using fallback data:', error);
    
    // Return fallback data with appropriate headers
    return NextResponse.json(FALLBACK_REGIONS, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
        'X-Fallback': 'true',
      },
    });
  }
}
